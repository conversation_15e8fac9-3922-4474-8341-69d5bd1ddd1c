import abc
import re
from typing import List, Dict, Any, Tuple, Optional

from ai_app.models.chat import ChatModelUsage # 保持对通用模型的引用
from ai_app.utils.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

# Default timeout for HTTP requests
DEFAULT_TIMEOUT = 60.0

class BaseLLMImpl(abc.ABC):
    """与 LLM 服务交互的抽象基类。"""

    @abc.abstractmethod
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        timeout: float = DEFAULT_TIMEOUT,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, str]] = None # For JSON mode
    ) -> Tuple[str, ChatModelUsage, Dict[str, Any]]:
        """
        与 LLM 进行聊天补全交互的核心方法。

        Args:
            messages: 对话消息列表，格式遵循 OpenAI 风格 [{ "role": "user/system/assistant", "content": "..." }]。
            timeout: 请求超时时间 (秒)。
            temperature: 控制生成文本的随机性。
            top_p: 控制核心采样的概率阈值。
            max_tokens: 生成响应的最大 token 数量。
            response_format: 指定响应格式，例如 {"type": "json_object"}。

        Returns:
            Tuple[str, ChatModelUsage, Dict[str, Any]]: 一个包含以下内容的元组:
                - 响应消息的内容 (str)
                - 包含模型ID和token使用量的 ChatModelUsage 对象
                - 原始的API响应字典 (Dict[str, Any])

        Raises:
            LLMAPIError: 如果 API 调用失败 (网络错误, 超时, 服务端错误等)。
            LLMResponseError: 如果 API 响应格式不正确或无法解析。
        """
        raise NotImplementedError
    
    def get_thinking_content(self, response: Dict[str, Any]) -> Optional[str]:
        """
        从 LLM 返回的内容中提取思考过程。
        """
        if 'choices' in response and len(response['choices']) > 0:
            choice = response['choices'][0] # 获取第一个choice
            if 'message' in choice:
                message = choice['message']
                if 'reasoning_content' in message:
                    reasoning_content = message['reasoning_content']
                    if isinstance(reasoning_content, str) and len(reasoning_content) > 0:
                        return reasoning_content
            if 'content' in message:
                content = message['content']
                if isinstance(content, str) and len(content) > 0:
                    thinking_content, _ = self.extract_think_label(content)
                    if thinking_content is not None:
                        return thinking_content
        return None
    
    def extract_think_label(self, content: str) -> Tuple[Optional[str], str]:
        """
        有的think内容会用<think>标签包裹，有的不会
        这里判断是否用<think>标签包裹，并返回包裹的内容和清理后的内容
        """
        think_label_pattern = r'<think>(.*?)</think>(.*)'
        # 使用 re.DOTALL 标志，使得 `.` 可以匹配包括换行符在内的任意字符
        match = re.search(think_label_pattern, content, re.DOTALL)
        if match:
            # 提取并清理思考过程和剩余内容
            thinking_content = match.group(1).strip()
            remaining_content = match.group(2).strip()
            return thinking_content, remaining_content
        return None, content
    
    def clean_content(self, content: str) -> str:
        """
        从 LLM 返回的内容中移除可能的包裹标签，包括：
        - <think> 和 </think> 标签
        - markdown JSON 包裹: ```json 和 ```
        - XML 节点标签: <xxx> 和 </xxx>
        - 混合使用的各种标签组合
        
        处理逻辑：
        1. 去除前后空白字符（包括空格、制表符、换行符等）
        2. 如果内嵌了think标签，先拿掉它们
        3. 迭代地从开头和结尾移除各种包裹标签
        4. 直到没有更多标签可以移除为止
        """
        # 使用更彻底的空白字符清理方法
        # 不仅移除空格，还移除制表符(\t)、换行符(\n)、回车符(\r)、换页符(\f)、垂直制表符(\v)等
        content = content.strip(' \t\n\r\f\v')

        # 先移除think标签
        _, content = self.extract_think_label(content)
        
        # 定义需要移除的标签模式
        # 开头标签模式：```json, ```, <任意标签>
        start_patterns = [
            r'^```json\s*',  # ```json 开头
            r'^```\s*',      # ``` 开头  
            r'^<[^>]+>\s*'   # XML 开头标签，如 <xxx>
        ]
        
        # 结尾标签模式：```, </任意标签>
        end_patterns = [
            r'\s*```$',      # ``` 结尾
            r'\s*</[^>]+>$'  # XML 结尾标签，如 </xxx>
        ]
        
        # 迭代移除标签，直到没有更多标签可以移除
        previous_content = ""
        while previous_content != content:
            previous_content = content
            
            # 移除开头的标签
            for pattern in start_patterns:
                match = re.match(pattern, content)
                if match:
                    content = content[match.end():]
                    logger.debug(f"Removed start wrapper: {match.group()}")
                    break
            
            # 移除结尾的标签
            for pattern in end_patterns:
                match = re.search(pattern, content)
                if match:
                    content = content[:match.start()]
                    logger.debug(f"Removed end wrapper: {match.group()}")
                    break
            
            # 再次彻底去除空白字符
            content = content.strip(' \t\n\r\f\v')
        
        return content 