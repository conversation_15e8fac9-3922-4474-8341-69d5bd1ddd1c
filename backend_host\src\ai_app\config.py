import os
import logging
import logging.config
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 统一配置环境变量默认值

@dataclass
class ConfigManager:
    bailian_app_base: str = os.getenv("BAILIAN_APP_BASE")
    bailian_app_id: str = os.getenv("BAILIAN_APP_ID", "YOUR_APP_ID")
    bailian_api_base: str = os.getenv("BAILIAN_API_BASE")
    bailian_api_key: str = os.getenv("BAILIAN_API_KEY", "YOUR_API_KEY")
    bailian_model: str = os.getenv("BAILIAN_MODEL")
    qwen3_enable_thinking: bool = os.getenv("QWEN3_ENABLE_THINKING").lower() == 'true'

    coze_app_base: str = os.getenv("COZE_APP_BASE")
    coze_api_key: str = os.getenv("COZE_API_KEY", "YOUR_API_KEY")
    coze_workflow_id: str = os.getenv("COZE_WORKFLOW_ID", "YOUR_WORKFLOW_ID")
    
    volcano_api_base: str = os.getenv("VOLCANO_API_BASE")
    volcano_api_key: str = os.getenv("VOLCANO_API_KEY", "YOUR_API_KEY")
    volcano_model: str = os.getenv("VOLCANO_MODEL")
    doubao_seed_enable_thinking: bool = os.getenv("DOUBAO_SEED_ENABLE_THINKING").lower() == 'true'

    # 火山向量数据库配置
    volcano_knowledge_api_base: str = os.getenv("VOLCANO_KNOWLEDGE_API_BASE", "https://api-knowledgebase.mlp.cn-beijing.volces.com")
    volcano_knowledge_api_ak: str = os.getenv("VOLCANO_KNOWLEDGE_API_AK", "YOUR_API_AK")
    volcano_knowledge_api_sk: str = os.getenv("VOLCANO_KNOWLEDGE_API_SK", "YOUR_API_SK")
    volcano_knowledge_collection_name: str = os.getenv("VOLCANO_KNOWLEDGE_COLLECTION_NAME", "")
    volcano_knowledge_project: str = os.getenv("VOLCANO_KNOWLEDGE_PROJECT", "default")
    faq_file_path: str = os.getenv("FAQ_FILE_PATH", "./src/ai_app/agents/faq_filter_agent/data/faq_doc.json")
    rewrite_prompt_path: str = os.getenv("REWRITE_PROMPT_PATH", "./src/ai_app/agents/faq_filter_agent/prompts/rewrite_prompt.md")
    classify_prompt_path: str = os.getenv("CLASSIFY_PROMPT_PATH", "./src/ai_app/agents/faq_filter_agent/prompts/classify_prompt.md")

    rerank_api_base: str = os.getenv("RERANK_API_BASE")
    rerank_api_key: str = os.getenv("RERANK_API_KEY", "YOUR_API_KEY")
    rerank_model: str = os.getenv("RERANK_MODEL")
    
    fastapi_host: str = os.getenv("FASTAPI_HOST", "0.0.0.0")
    fastapi_port: int = int(os.getenv("FASTAPI_PORT", 8000))
    app_log_level: str = os.getenv("APP_LOG_LEVEL", "")

    def __post_init__(self):
        print("Configuration loaded:")
        for field, value in self.__dict__.items():
            if field.endswith("_api_key") or field.endswith("_api_ak") or field.endswith("_api_sk"):
                print(f"{field}: {value[:12] + '****' + value[-6:] if value else ''}")
            else:
                print(f"{field}: {value}")

    def get_model_config(self, model_platform: str):
        if model_platform == "volcano":
            return self.volcano_api_key, self.volcano_api_base, self.volcano_model
        elif model_platform == "bailian":
            return self.bailian_api_key, self.bailian_api_base, self.bailian_model
        else:
            raise ValueError(f"Invalid model platform: {model_platform}")
        
    def get_rerank_config(self):
        if self.rerank_api_key == "YOUR_API_KEY":
            return None, None, None
        return self.rerank_api_key, self.rerank_api_base, self.rerank_model

    def get_volcano_knowledge_config(self):
        """获取火山向量数据库配置。

        Returns:
            Tuple[str, str, str, str, str]: (api_base, api_ak, api_sk, collection_name, project)
        """
        if self.volcano_knowledge_api_ak == "YOUR_API_AK" or self.volcano_knowledge_api_sk == "YOUR_API_SK":
            return None, None, None, None, None
        return (
            self.volcano_knowledge_api_base,
            self.volcano_knowledge_api_ak,
            self.volcano_knowledge_api_sk,
            self.volcano_knowledge_collection_name,
            self.volcano_knowledge_project
        )
        
    # 检查必要的环境变量是否已设置
    def check_bailian_vars(self):
        if self.bailian_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 BAILIAN_API_KEY")

    def check_coze_vars(self):
        if self.coze_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 COZE_API_KEY")

    def check_volcano_vars(self):
        if self.volcano_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 VOLCANO_API_KEY")

    def check_volcano_knowledge_vars(self):
        if self.volcano_knowledge_api_ak == "YOUR_API_AK" or self.volcano_knowledge_api_sk == "YOUR_API_SK":
            raise ValueError("请在 .env 文件中设置 VOLCANO_KNOWLEDGE_API_AK 和 VOLCANO_KNOWLEDGE_API_SK")
        if not self.volcano_knowledge_collection_name:
            raise ValueError("请在 .env 文件中设置 VOLCANO_KNOWLEDGE_COLLECTION_NAME")
        
        
config = ConfigManager()

# 日志配置函数
def get_logging_config(cli_verbose_flag: bool | None = None) -> dict:
    """
    生成日志配置字典。
    优先级: APP_LOG_LEVEL 环境变量 > cli_verbose_flag > 默认 "INFO"
    """
    env_log_level = config.app_log_level.upper()
    
    final_log_level = "INFO" # 默认

    if env_log_level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
        final_log_level = env_log_level
        logging.info(f"Log level set to '{final_log_level}' from APP_LOG_LEVEL environment variable.")
    elif cli_verbose_flag is True:
        final_log_level = "DEBUG"
        logging.info(f"Log level set to 'DEBUG' from command-line verbose flag.")
    else:
        logging.info(f"Log level set to default 'INFO'.")

    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": "%(levelprefix)s %(asctime)s - %(name)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "use_colors": None, # 在 Docker 中通常不建议使用颜色，除非有特殊处理
            },
            "access": {
                "()": "uvicorn.logging.AccessFormatter",
                "fmt": '%(levelprefix)s %(asctime)s - %(client_addr)s - "%(request_line)s" %(status_code)s',
                 "datefmt": "%Y-%m-%d %H:%M:%S",
                 "use_colors": None,
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
            },
             "access": {
                "formatter": "access",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["default"],
                "level": final_log_level,
                "propagate": False
            },
            "uvicorn.error": { # uvicorn 错误日志
                "level": "INFO", # 通常保持 INFO 或更高，除非特别需要 DEBUG uvicorn 内部
                "handlers": ["default"],
                "propagate": False,
            },
            "uvicorn.access": { # uvicorn 访问日志
                "handlers": ["access"],
                "level": "INFO", # 通常保持 INFO
                "propagate": False,
            },
            # 可以为你的应用程序特定模块添加 logger 配置
            # "your_app_module_name": {
            #     "handlers": ["default"],
            #     "level": final_log_level, # 与根 logger 一致或单独设置
            #     "propagate": False,
            # },
        },
    }

# 模块加载时应用一次日志配置 (主要服务于 Docker/直接 Uvicorn 启动场景)
# 这时 cli_verbose_flag 为 None，主要依赖环境变量或默认值
logging.config.dictConfig(get_logging_config())
