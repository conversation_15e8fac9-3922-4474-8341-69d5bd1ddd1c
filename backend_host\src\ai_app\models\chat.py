from pydantic import BaseModel, Field
from typing import Optional, Dict, List

# 前端请求体结构
class ChatInputMessage(BaseModel):
    """聊天输入消息结构"""
    role: str = Field(..., description="消息发送者的角色（例如 'user', 'assistant'）")
    content: str = Field(..., description="消息内容")

class ChatRequest(BaseModel):
    """发送给后端进行聊天的请求体"""
    conversation: List[ChatInputMessage] = Field(..., description="包含历史消息的对话列表")
    session_id: Optional[str] = Field(None, description="可选的会话 ID，用于保持对话上下文(暂时没用)")
    service: Optional[str] = Field(None, description="可选的后端服务标识（例如 'volcano'（默认）, 'bailian'）")
    context_params: Optional[Dict] = Field(None, description="可选的、传递给特定服务的额外上下文参数，比如channel、platform")

# 后端返回给前端的结构
class ChatModelUsage(BaseModel):
    """单个模型调用的 token 使用情况"""
    model_id: Optional[str] = Field(None, description="被调用模型的 ID")
    input_tokens: Optional[int] = Field(None, description="输入消耗的 token 数量")
    output_tokens: Optional[int] = Field(None, description="输出消耗的 token 数量")

class ChatModelUsages(BaseModel):
    """多个模型调用的 token 使用情况汇总"""
    models: Optional[List[ChatModelUsage]] = Field(None, description="包含所有模型调用使用情况的列表")

class ChatResponse(BaseModel):
    """后端返回给前端的聊天响应体"""
    response_code: int = Field(..., description="响应码（200为成功，其他均为失败）")
    response_text: Optional[str] = Field(None, description="错误文本（如果有报错）")
    session_id: Optional[str] = Field(None, description="本次交互使用的或新生成的会话 ID")
    usages: Optional[ChatModelUsages] = Field(None, description="模型调用的 token 使用情况统计") 