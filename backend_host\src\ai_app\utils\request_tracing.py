"""
请求追踪工具模块

提供轻量级的请求追踪功能，与现有日志配置完全兼容。
支持通过HTTP Header传递request_id，并在所有日志中自动添加追踪信息。
"""

import uuid
import logging
from contextvars import ContextVar
from typing import Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

# 使用 ContextVar 在异步环境中传递 request_id
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)

# 请求追踪配置（统一使用小写格式）
REQUEST_ID_HEADER = "x-request-id"


class RequestTracingMiddleware(BaseHTTPMiddleware):
    """
    请求追踪中间件
    
    功能：
    1. 从HTTP Header中获取或生成request_id
    2. 将request_id设置到上下文变量中
    3. 在响应Header中返回request_id
    4. 记录请求开始和结束日志
    """
    
    async def dispatch(self, request: Request, call_next):
        # 获取或生成 request_id
        request_id = request.headers.get(REQUEST_ID_HEADER)
        if not request_id:
            request_id = str(uuid.uuid4())
        
        # 设置到上下文变量
        request_id_var.set(request_id)
        
        # 设置到request.state，方便在路由中访问
        request.state.request_id = request_id
        
        # 记录请求开始日志
        logger = get_traced_logger(__name__)
        logger.info(f"Request started: {request.method} {request.url.path}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 在响应头中返回 request_id
            response.headers[REQUEST_ID_HEADER] = request_id
            
            # 记录请求完成日志
            logger.info(f"Request completed: {response.status_code}")
            
            return response
            
        except Exception as e:
            # 记录请求异常日志
            logger.error(f"Request failed: {str(e)}")
            raise
        finally:
            # 清理上下文变量
            request_id_var.set(None)


class TracedLogger:
    """
    带追踪信息的日志记录器
    
    自动在日志消息中添加request_id前缀，与现有日志配置完全兼容。
    """
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def _format_message(self, message: str) -> str:
        """格式化日志消息，添加request_id前缀"""
        request_id = request_id_var.get()
        if request_id:
            return f"[{request_id}] {message}"
        return message
    
    def debug(self, message: str, *args, **kwargs):
        """记录DEBUG级别日志"""
        self.logger.debug(self._format_message(message), *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """记录INFO级别日志"""
        self.logger.info(self._format_message(message), *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """记录WARNING级别日志"""
        self.logger.warning(self._format_message(message), *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """记录ERROR级别日志"""
        self.logger.error(self._format_message(message), *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """记录CRITICAL级别日志"""
        self.logger.critical(self._format_message(message), *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """记录异常日志"""
        self.logger.exception(self._format_message(message), *args, **kwargs)


def get_traced_logger(name: str) -> TracedLogger:
    """
    获取带追踪信息的日志记录器
    
    Args:
        name: 日志记录器名称，通常使用 __name__
    
    Returns:
        TracedLogger: 带追踪信息的日志记录器实例
    """
    return TracedLogger(name)


def get_current_request_id() -> Optional[str]:
    """
    获取当前请求的request_id
    
    Returns:
        Optional[str]: 当前请求的request_id，如果不在请求上下文中则返回None
    """
    return request_id_var.get()


def set_request_id(request_id: Optional[str] = None) -> str:
    """
    手动设置request_id（主要用于测试场景）

    Args:
        request_id: 要设置的request_id，如果为None则生成新的UUID

    Returns:
        str: 实际设置的request_id
    """
    if not request_id:
        request_id = str(uuid.uuid4())
    request_id_var.set(request_id)
    return request_id


def clear_request_id() -> None:
    """
    清除当前的request_id（主要用于测试场景）
    """
    request_id_var.set(None)
