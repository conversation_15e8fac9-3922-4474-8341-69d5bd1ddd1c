import json
from fastapi import APIRouter, HTTPException

from ai_app.config import config
from ai_app.agents.faq_filter_agent.agent import FAQFilterAgent
from ai_app.models.chat import ChatRequest
from ai_app.models.chat_to_faq_filter import ChatToFaqFilterResponse
from ai_app.utils.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

router = APIRouter()

@router.post("/chat/faq_filter", response_model=ChatToFaqFilterResponse)
async def chat_to_faq_filter(chat_request: ChatRequest):
    """处理到 FAQ Filter Agent 的聊天请求。"""
    logger.info("=========== /chat/faq_filter endpoint received request ==========")
    try:
        logger.debug(f"Request:\n{json.dumps(chat_request.model_dump(), indent=2, ensure_ascii=False)}")
        # 校验模型平台并记录日志
        if chat_request.service == None or chat_request.service == "":
            logger.debug("No model platform specified, defaulting to Volcano.")
            chat_request.service = "volcano"
        if chat_request.service == "volcano":
            logger.info("Routing request to Volcano agent.")
            config.check_volcano_vars()
        elif chat_request.service == "bailian":
            logger.info("Routing request to Bailian agent.")
            config.check_bailian_vars()
        else:
            logger.error(f"Invalid model platform specified for agent: {chat_request.service}")
            raise HTTPException(status_code=400, detail=f"Invalid model platform for agent: {chat_request.service}")

        # 初始化并调用 Agent
        faq_filter_agent = FAQFilterAgent(chat_request.context_params, model_platform=chat_request.service)
        response = await faq_filter_agent.process_user_request(chat_request) # 调用 Custom Agent 服务
        logger.debug(f"Response:\n{json.dumps(response.model_dump(), indent=2, ensure_ascii=False)}")
        return response

    except HTTPException as e:
        # 直接重新抛出由服务层或本层抛出的 HTTPException
        raise e
    except Exception as e:
        # 捕获未预料的错误
        logger.exception("An unexpected error occurred in /chat/faq_filter endpoint")
        raise HTTPException(status_code=500, detail="Internal server error in FAQ Filter agent processing.")
