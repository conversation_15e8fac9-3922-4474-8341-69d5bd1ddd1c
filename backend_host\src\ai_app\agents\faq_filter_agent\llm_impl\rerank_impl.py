from typing import List, Optional, Dict, Tuple, Any
import httpx
import json
from ai_app.agents.faq_filter_agent.exceptions import LLMAPIError, LLMResponseError
from ai_app.models.chat import ChatModelUsage
from ai_app.utils.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

DEFAULT_TIMEOUT = 20.0
DEFAULT_TOP_N = 3

class RerankImpl:
    def __init__(self, api_key: str, api_base: str, model_id: str):
        self.api_key = api_key
        self.api_base = api_base
        self.model_id = model_id
        self.headers = {
            "Content-type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

    async def rerank(self, query: str, documents: List[str],
        timeout: float = DEFAULT_TIMEOUT,
        top_n: int = DEFAULT_TOP_N,
    ) -> <PERSON><PERSON>[List[Dict[str, Any]], Chat<PERSON><PERSON><PERSON>Usage, Dict[str, Any]]:
        request_body = {
            "model": self.model_id,
            "top_n": top_n,
            "query": query,
            "documents": documents,
        }
        logger.debug(f"Calling Rerank API: {self.api_base} with model {self.model_id}")
        logger.debug(f"Request Body: {json.dumps(request_body, ensure_ascii=False, indent=2)}")
        async with httpx.AsyncClient(timeout=timeout) as client:
            try:
                response = await client.post(
                    f"{self.api_base}/rerank",
                    headers=self.headers,
                    json=request_body
                )
                response.raise_for_status()
            except httpx.TimeoutException as e:
                logger.error(f"Rerank API request timed out to {self.api_base}: {e}")
                raise LLMAPIError(f"Request timed out after {timeout}s: {e}") from e
            except httpx.RequestError as e:
                logger.error(f"Rerank API request error to {self.api_base}: {e}")
                raise LLMAPIError(f"Request failed: {e}") from e
            except httpx.HTTPStatusError as e:
                error_detail = e.response.text
                try:
                    error_json = e.response.json()
                    error_detail = error_json.get('error', {}).get('message', error_detail)
                except json.JSONDecodeError:
                    pass
                logger.error(f"Rerank API returned error status {e.response.status_code} from {self.api_base}: {error_detail}")
                raise LLMAPIError(f"API returned status {e.response.status_code}: {error_detail}") from e

        try:
            response_data = response.json()
            logger.debug(f"Raw Rerank API response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            if 'results' not in response_data:
                logger.error(f"Unexpected response structure from Rerank API. Missing 'results'. Response: {response_data}")
                raise LLMResponseError("Unexpected API response structure: Missing results.")

            results = response_data['results']
            format_results = []
            for result in results:
                format_result = {
                    "index": result['index'],
                    "score": result['relevance_score']
                }
                format_results.append(format_result)

            usage = ChatModelUsage(
                model_id=response_data.get('model', self.model_id),
                input_tokens=response_data.get('usage', {}).get('total_tokens', 0),
                output_tokens=0
            )
            return format_results, usage, response_data
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response from Rerank API: {response.text}")
            raise LLMResponseError(f"Failed to decode API JSON response: {e}") from e
        except (KeyError, IndexError, TypeError) as e:
             logger.error(f"Failed to parse expected data from Rerank API response. Response: {response_data}. Error: {e}", exc_info=True)
             raise LLMResponseError(f"Unexpected API response structure: {e}") from e 