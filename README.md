# AI聊天网页Demo项目

## 项目概述
基于React和FastAPI实现的AI聊天网页Demo，前端提供简洁聊天界面，后端作为百炼、coze、火山方舟等平台API代理。

## 技术栈
- 前端：React + Vite + Axios
- 后端：FastAPI + Uvicorn + Pydantic + python-dotenv + httpx + uv (用于环境和包管理)
- 部署：开发模式运行 + docker运行（可选）

## 功能特性
1. 用户消息发送与显示
2. AI回复接收与显示
3. 简单的错误处理

## 配置说明
1. 环境变量配置：
   - 复制示例文件并重命名：
     ```bash
     cp backend_host/.env.example backend_host/.env
     cp frontend/.env.example frontend/.env
     ```
   - 后端配置(`backend_host/.env`):
     - BAILIAN_APP_BASE: 百炼平台API地址
     - BAILIAN_API_KEY: 百炼平台API密钥
     - COZE_APP_BASE: Coze平台API基础地址
     - COZE_API_KEY: Coze平台API密钥
     - COZE_WORKFLOW_ID: Coze平台要运行的工作流ID
     - 根据 backend_host/.env.example 补充其他平台
   - 前端配置(`frontend/.env`):
     - REACT_APP_API_BASE_URL: 后端服务地址(默认http://localhost:8000/api)
2. Python 版本:
   - 项目后端推荐使用 Python 3.11，该版本在 `backend_host/.python-version` 文件中指定。请确保你的 `uv` 或 `pyenv` 等Python版本管理工具能够识别此文件。

## 快速开始

### 前端启动
```bash
cd frontend
npm install
npm start
```

### 后端启动
```bash
cd backend_host

# 确保已安装 uv (请参考官方文档: https://docs.astral.sh/uv/getting-started/installation/)

# 1. 创建并激活虚拟环境 (uv 会自动查找并使用 .python-version 文件中指定的 Python 3.11)
uv venv
source .venv/bin/activate  # 对于 Linux/macOS
# .venv\Scripts\activate  # 对于 Windows CMD
# .venv\Scripts\Activate.ps1 # 对于 Windows PowerShell

# 2. 同步依赖 (基于 uv.lock 文件，确保环境与锁文件一致)
uv sync

# 3. 启动后端服务
uv run python -m ai_app.server.main -v
```

## 项目结构
```
project-root/
├── docker/          # Docker 配置目录 (Dockerfile, docker-compose.yml 等)
│   ├── backend_host/     # 后端 Dockerfile 目录
│   ├── frontend/    # (待创建) 前端 Dockerfile 目录
│   ├── docker-compose.yml
│   └── README.md    # Docker 配置详细说明
├── frontend/        # React前端
│   ├── public/
│   ├── src/        # 源代码
│   ├── .env.example
│   ├── index.html
│   ├── package.json # 依赖配置
│   └── vite.config.js
├── backend_host/         # FastAPI后端 (使用 uv 管理环境和依赖)
│   ├── src/          # 源码目录
│   │   └── ai_app/     # <--- 主要应用代码目录
│   │       ├── __init__.py # <--- 使 ai_app 成为一个包
│   │       ├── config.py   # 配置加载
│   │       ├── server/     # FastAPI 应用核心 (应用入口、路由等)
│   │       │   ├── main.py      # FastAPI 应用入口
│   │       │   └── routers/    # API路由
│   │       ├── agents/     # AI-agent实现的服务
│   │       │   └── faq_filter_agent # faq筛选器agent（详见目录内的`README.md`）
│   │       ├── models/     # Pydantic模型 (数据校验与结构定义)
│   │       └── services/   # 外部服务调用逻辑
│   │           ├── bailian.py # 百炼应用API调用实现
│   │           └── coze.py    # Coze平台API调用实现
│   ├── .venv/      # Python 虚拟环境 (由 uv 创建, 通常 gitignored)
│   ├── .env.example # 环境变量示例
│   ├── .env        # 环境配置 (gitignored)
│   ├── pyproject.toml # 项目元数据和依赖声明 (PEP 621)
│   ├── uv.lock     # 精确的依赖锁文件 (保证可复现构建)
│   └── .python-version # 指定项目使用的 Python 版本 (如 3.11)
└── README.md       # 项目文档
```

## 注意事项
1. 确保已安装Node.js (>=16) 和 Python (3.11, 通过 `backend_host/.python-version` 指定) 环境。
2. 后端项目已迁移至使用 `uv` 进行包和环境管理。请参考后端启动说明安装和使用 `uv`。
3. 开发时需同时运行前后端服务。
4. 生产环境部署需要考虑使用docker。
5. 请务必在 `.env` 文件中配置好所需的 API Key 和 URL。

## Docker化部署

本项目支持使用 Docker 和 Docker Compose 进行容器化部署。
所有相关的配置文件（包括各个服务的 `Dockerfile` 和 `docker-compose.yml`）都位于 `docker/` 文件夹中。

有关 Docker 环境的详细设置、构建步骤、运行命令以及各服务配置详情，请参阅该目录下的 `docker/README.md` 文件。