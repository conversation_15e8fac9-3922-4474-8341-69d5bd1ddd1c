import React, { useState } from 'react';

// 会话信息控件组件
function SessionInfo({ sessionId, usages }) {
  const [isExpanded, setIsExpanded] = useState(false);

  // 如果既没有 sessionId 也没有 usage.models，则不渲染
  if (!sessionId && (!usages || !usages.models || usages.models.length === 0)) {
    return null;
  }

  return (
    <div className="session-info-container">
      <span className="session-toggle-icon" onClick={() => setIsExpanded(!isExpanded)}>
        {isExpanded ? '▲' : '▼'}
      </span>
      {isExpanded && (
        <div className="session-details-bubble">
          {sessionId && <div className="session-id-info">SessionID: {sessionId}</div>}

          {usages?.models?.map((model, index) => {
            const inputTokens = model.input_tokens ?? 0; // 使用 ?? 提供默认值 0
            const outputTokens = model.output_tokens ?? 0;
            const totalTokens = inputTokens + outputTokens;
            return (
              <div key={index} className="usage-model-info">
                {`Model${index + 1}: ${model.model_id || 'N/A'}; Input: ${inputTokens}; Output: ${outputTokens}; Total: ${totalTokens}`}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default SessionInfo; 