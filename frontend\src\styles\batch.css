.batch-test-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 64px); /* 减去导航栏高度 */
  background-color: #f3f6fa;
  padding: 20px;
  font-family: 'Segoe UI', 'Robot<PERSON>', 'Helvetica Neue', <PERSON><PERSON>, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.batch-content {
  width: 80vw;
  max-width: 800px;
  min-height: 60vh;
  border: none;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08), 0 1.5px 4px rgba(0,0,0,0.03);
  background: #fff;
  padding: 40px;
  text-align: center;
}

.batch-content h2 {
  color: #007bff;
  margin-bottom: 20px;
  font-size: 2em;
  font-weight: 600;
}

.batch-content p {
  color: #666;
  font-size: 1.1em;
  margin-bottom: 30px;
}

.coming-soon {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.feature-list {
  background: #f8fafc;
  border-radius: 12px;
  padding: 30px;
  border: 1px solid #e0e0e0;
  max-width: 500px;
}

.feature-list h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.2em;
  font-weight: 600;
}

.feature-list ul {
  text-align: left;
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 8px 0;
  color: #555;
  font-size: 1em;
  display: flex;
  align-items: center;
}

.feature-list li:not(:last-child) {
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 8px;
  padding-bottom: 16px;
}

@media (max-width: 768px) {
  .batch-test-container {
    padding: 10px;
    min-height: calc(100vh - 56px);
  }
  
  .batch-content {
    width: 100%;
    padding: 30px 20px;
  }
  
  .batch-content h2 {
    font-size: 1.6em;
  }
  
  .feature-list {
    padding: 20px;
  }
} 