# AI Agent - 游戏客服 FAQ 筛选器

## 概述

本模块实现了一个基于FastAPI的AI聊天网页Demo项目的核心组件，作为多AI平台(百炼、Coze、火山方舟)的API代理，实现了完整的FAQ筛选器Agent。核心包含查询重写→问题分类→向量召回→答案检索→重排序的完整AI工作流，使用Python 3.11+uv包管理，支持模块化LLM实现和Docker部署。

该模块设计为可被上层服务（如 `backend_host/src/ai_app/server/`）集成使用，同时也支持独立的测试和开发。支持多渠道FAQ数据管理，具备分类失败时的向量召回补充机制。

## 核心工作流

1.  **输入**: 接收玩家与客服的对话历史 (`conversation`) 以及一些额外的上下文信息 (`context`，例如渠道、平台）。
2.  **查询重写 (Query Rewrite)**: 使用专门的LLM，结合对话历史和上下文，将用户的原始问题重写为一个清晰、独立且包含必要背景信息的查询语句 (`query_rewrite`)。
3.  **FAQ 目录提取**: 从 `data/faq_doc.json` 文件中解析 FAQ 数据，并提取出 Markdown 格式的目录结构，其中包含类别键 (`category_key`) 和类别描述 (`category_desc`)。支持多渠道FAQ文件（如 `faq_doc-zulong.json`）。
4.  **问题分类 (Classification)**: 使用LLM，根据重写后的查询 (`query_rewrite`) 和 FAQ 目录结构，判断查询最符合哪个具体的 FAQ 类别。LLM 被要求输出一至多行文本，每行包含类别键路径 (`category_key_path`，例如 `1.1.2`、`1.2.0` 或 `0`) 。
5.  **向量召回 (Retrieval Fallback)**: 当分类结果为空或仅包含 `category_key_path` 为 "0" 的项目时，自动启用向量数据库召回机制，使用火山向量数据库进行语义检索，最多召回3条相关结果作为补充。
6.  **答案检索**: 根据分类或召回得到的 `category_key_path`，在 `data/faq_doc.json` 等文件中查找并提取对应的最终答案 (`answer`) 和问题示例 (`question_example`)。
7.  **答案重排序（可选）**: 使用专门的重排序模型，根据玩家问题 (`query_rewrite`) 和检索到的答案，对答案进行重排序，以确保答案的准确性和相关性。
8.  **输出**: 返回检索到的答案列表，包含答案内容、分类链、得分和推理过程。如果分类失败或找不到答案，则返回相应的保底话术。

## 主要组件

### 核心模块

*   **`agent.py` (`FAQFilterAgent`)**: Agent 的主入口和协调器，负责编排整个工作流程，包括分类失败时的向量召回补充机制。
*   **`data_parser.py` (`FAQDataParser`)**: 负责加载、解析 `data/faq_doc.json` 文件，提供获取 Markdown 目录结构、根据 `category_key_path` 查询答案、以及双向路径转换的功能。
*   **`llm_clients.py`**:
    *   `QueryRewriteClient`: 封装与执行查询重写任务的LLM的API交互。
    *   `FAQClassifierClient`: 封装与执行问题分类任务的LLM的API交互。
    *   `FAQRetrieveClient`: 封装向量数据库检索功能，支持语义召回补充。
    *   `FAQRerankClient`: 封装与执行答案重排序任务的LLM的API交互。

### LLM实现层

*   **`llm_impl/`**: 多平台LLM实现
    *   `volcano_impl.py`: 火山引擎LLM和向量数据库实现
    *   `bailian_impl.py`: 百炼平台LLM实现
    *   `rerank_impl.py`: 重排序模型实现

### 数据和配置

*   **`prompts/`**: 存放用于指导 LLM 的 Prompt 模板文件。
    *   `rewrite_prompt.md`: 查询重写任务的 Prompt。
    *   `classify_prompt.md`: 问题分类任务的 Prompt。
*   **`data/`**: 存放数据文件和转换工具。
    *   `faq_doc.json`: 结构化的 FAQ 知识库。
    *   `faq_doc-zulong.json`: 祖龙渠道专用FAQ知识库。
    *   `faq.xlsx`: 原始Excel格式FAQ数据。
    *   `excel_converter.py`: Excel与JSON双向转换工具。

### 测试模块

*   **`tests/`**: 测试文件
    *   `test_llm_clients.py`: LLM客户端测试，支持查询重写、分类、召回等功能测试。

## 配置

系统通过 `backend_host/src/ai_app/config.py` 进行配置管理，支持以下配置项：

### LLM平台配置
*   **百炼平台**: `bailian_api_key`, `bailian_api_base`, `bailian_model`
*   **火山引擎**: `volcano_api_key`, `volcano_api_base`, `volcano_model`
*   **Coze平台**: `coze_api_key`, `coze_app_base`, `coze_workflow_id`

### 向量数据库配置
*   **火山知识库**: `volcano_knowledge_api_base`, `volcano_knowledge_api_ak`, `volcano_knowledge_api_sk`
*   **集合配置**: `volcano_knowledge_collection_name`, `volcano_knowledge_project`

### 重排序配置
*   **重排序API**: `rerank_api_base`, `rerank_api_key`, `rerank_model`

### 文件路径配置
*   **FAQ数据**: `faq_file_path`
*   **Prompt模板**: `rewrite_prompt_path`, `classify_prompt_path`

配置可通过环境变量或配置文件设置，支持多渠道FAQ文件自动切换。

## 使用

### 基本使用

上层服务（如 `backend_host/src/ai_app/server/`）可以通过导入 `FAQFilterAgent` 类并调用其处理方法来使用此模块：

```python
from ai_app.agents.faq_filter_agent.agent import FAQFilterAgent
from ai_app.models.chat import ChatRequest

# 初始化Agent
agent = FAQFilterAgent(
    context_params={'channel_name': 'zulong'},
    model_platform='volcano'
)

# 处理用户请求
response = await agent.process_user_request(chat_request)
```

### 数据管理

#### Excel与JSON转换

```bash
# Excel转JSON
python excel_converter.py excel-to-json faq.xlsx --output faq_doc.json

# JSON转Excel
python excel_converter.py json-to-excel faq_doc.json --output faq_output.xlsx
```

#### 支持的数据格式

**Excel格式**（7列）：
1. 分类层级1-5
2. 答案内容
3. 问题示例

**JSON格式**：
```json
{
    "category_key_path": "1.1.1",
    "category_desc": "找回密码",
    "answer": "专员，您好...",
    "question_example": "我忘记密码了，怎么找回"
}
```

## 测试

### 单元测试

使用 `tests/test_llm_clients.py` 进行各组件测试：

```bash
# 测试查询重写
python -m ai_app.agents.faq_filter_agent.tests.test_llm_clients rewrite \
  --query "我密码忘了" --model-platform volcano

# 测试问题分类
python -m ai_app.agents.faq_filter_agent.tests.test_llm_clients classify \
  --query "我忘记密码了" --faq-structure-file data/faq_doc.json

# 测试向量召回
python -m ai_app.agents.faq_filter_agent.tests.test_llm_clients retrieve \
  --query "我忘记密码了" --top-n 5
```

### 功能特性

✅ **多平台LLM支持**: 百炼、火山引擎、Coze
✅ **向量召回补充**: 分类失败时自动启用语义检索
✅ **多渠道FAQ**: 支持不同渠道的FAQ数据文件
✅ **双向数据转换**: Excel ↔ JSON 格式转换
✅ **完整工作流**: 重写→分类→召回→检索→重排序
✅ **错误处理**: 优雅降级和详细日志记录
✅ **Token统计**: 完整的模型使用量统计

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────┐
│              FAQFilterAgent             │  ← 主协调器
├─────────────────────────────────────────┤
│  QueryRewrite │ Classifier │ Retrieve   │  ← 业务逻辑层
│  Client       │ Client     │ Client     │
├─────────────────────────────────────────┤
│  VolcanoLLM   │ BailianLLM │ RerankImpl │  ← LLM实现层
│  Impl         │ Impl       │            │
├─────────────────────────────────────────┤
│  FAQDataParser │ ExcelConverter         │  ← 数据处理层
└─────────────────────────────────────────┘
```

### 数据流

```
用户查询 → 查询重写 → 问题分类 ┬→ 答案检索 → 重排序 → 返回结果
                           ↓
                      分类失败检测
                           ↓
                      向量召回补充
```

## 性能优化

### 召回策略
- **主路径**: LLM分类，精确匹配FAQ结构
- **补充路径**: 向量召回，语义相似度匹配
- **触发条件**: 分类为空或仅返回category_key_path="0"
- **召回限制**: 最多3条结果，避免噪声

### 缓存机制
- FAQ数据解析结果缓存
- 向量检索结果可缓存（未实现）
- LLM响应缓存（未实现）

## 错误处理

### 分级处理
1. **致命错误**: 配置缺失、文件不存在 → 抛出异常
2. **业务错误**: LLM调用失败 → 返回错误响应
3. **降级处理**: 召回失败 → 继续使用分类结果
4. **保底机制**: 无答案时返回保底话术

### 日志记录
- **INFO**: 正常流程和关键节点
- **WARNING**: 非致命错误和降级处理
- **ERROR**: 业务错误和异常情况
- **DEBUG**: 详细的调试信息

## 扩展指南

### 添加新的LLM平台
1. 在 `llm_impl/` 下创建新的实现类
2. 继承相应的接口（`LLMImpl`, `EmbeddingImpl`, `RerankImpl`）
3. 在 `config.py` 中添加配置项
4. 在 `agent.py` 中添加初始化逻辑

### 添加新的数据源
1. 扩展 `FAQDataParser` 支持新格式
2. 更新 `excel_converter.py` 支持新的转换
3. 修改配置文件路径逻辑

### 自定义召回策略
1. 扩展 `FAQRetrieveClient` 的触发条件
2. 调整召回结果的数量和过滤逻辑
3. 实现自定义的相似度计算