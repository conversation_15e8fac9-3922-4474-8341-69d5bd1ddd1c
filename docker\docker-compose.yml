services:
  backend_host:
    build:
      context: ../backend_host  # 构建上下文指向 monorepo 下的 backend_host/ 目录
      dockerfile: ../docker/backend_host/Dockerfile # Dockerfile 路径指向 docker/backend_host/Dockerfile（该路径相对于context）
    image: harbor.zulong.com/ai-assistant/kefu-backend:v0.1.9 # 添加此行来指定镜像名称和标签
    container_name: ai_kefu_backend_prod # 示例容器名称，请按需修改或移除
    ports:
      - "18000:8000" # 将宿主机的 18000 端口映射到容器的 8000 端口
    # volumes:
      # 如果您希望在开发时代码更改能立即反映到容器中，可以取消注释下一行
      # 但请注意，这通常用于开发，生产镜像构建时不应包含此卷挂载以确保镜像是自包含的
      # - ../backend_host:/app/backend_host # 注意：如果使用此选项，目标路径应与Dockerfile中代码路径一致
    env_file:
      - ./backend_host/.env  # 尝试从 docker/.env 加载环境变量
    #environment:
      #APP_LOG_LEVEL: INFO # 您也可以在这里直接设置环境变量，它会覆盖env_file中的同名变量
    restart: unless-stopped
    networks:
      - ai-kefu-network

  frontend:
    build:
      context: ../frontend  # 构建上下文指向 monorepo 下的 frontend/ 目录
      dockerfile: ../docker/frontend/Dockerfile # Dockerfile 路径指向 docker/frontend/Dockerfile（该路径相对于context）
    image: harbor.zulong.com/ai-assistant/kefu-frontend:v0.1.8 # 前端镜像名称和标签
    container_name: ai_kefu_frontend_prod # 前端容器名称
    ports:
      - "13000:80" # 将宿主机的 13000 端口映射到容器的 80 端口（Nginx）
    env_file:
      - ./frontend/.env  # 运行时加载环境变量（与后端保持一致）
    restart: unless-stopped
    networks:
      - ai-kefu-network

networks:
  ai-kefu-network:
    driver: bridge