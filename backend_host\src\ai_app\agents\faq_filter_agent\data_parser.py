import json
import os
from typing import List, Dict, Any, Optional, Tuple # Added Tuple
from ai_app.agents.faq_filter_agent.exceptions import FAQDataError # Import custom exception
from ai_app.utils.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

# TODO: Consider adding custom exceptions from exceptions.py

class FAQDataParser:
    """负责加载、解析和查询 FAQ JSON 数据。"""

    def __init__(self, faq_file_path: str):
        """初始化解析器，加载 FAQ 数据。

        Args:
            faq_file_path: faq_doc.json 文件的路径。

        Raises:
            FAQDataError: 如果加载或解析 FAQ 数据时出错。
        """
        self.faq_file_path = faq_file_path
        self.faq_data: List[Dict[str, Any]] = self._load_faq()

    def _load_faq(self) -> List[Dict[str, Any]]:
        """从 JSON 文件加载 FAQ 数据。"""
        # Check if file exists first for a clearer error message
        if not os.path.exists(self.faq_file_path):
            logger.error(f"FAQ file not found: {self.faq_file_path}")
            raise FAQDataError(f"FAQ file not found: {self.faq_file_path}")

        try:
            with open(self.faq_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if not isinstance(data, list):
                logger.error(f"FAQ data in {self.faq_file_path} is not a list.")
                raise FAQDataError("FAQ data structure is invalid: root element must be a list.")
            logger.debug(f"Successfully loaded FAQ data from {self.faq_file_path}")
            return data
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {self.faq_file_path}: {e}")
            raise FAQDataError(f"Failed to decode JSON from file: {e}") from e
        except IOError as e:
            logger.error(f"Error reading FAQ file {self.faq_file_path}: {e}")
            raise FAQDataError(f"Failed to read FAQ file: {e}") from e
        except Exception as e: # Catch unexpected errors
            logger.exception(f"An unexpected error occurred while loading FAQ file {self.faq_file_path}: {e}") # Use logger.exception for traceback
            raise FAQDataError(f"An unexpected error occurred: {e}") from e

    def get_category_structure_markdown(self, max_depth: int = -1) -> str:
        """生成 Markdown 格式的 FAQ 目录结构字符串，类似 test_faq_categories.md 格式。

        Args:
            max_depth: 最大递归层数。-1 表示无限制，正整数 n 表示最大生成 n 层目录。
                       例如，max_depth=1 只生成顶级目录，max_depth=2 生成顶级和二级目录。

        Returns:
            Markdown 格式的目录结构字符串。
        """
        logger.debug(f"Generating category structure markdown with max_depth={max_depth}")
        markdown_str = ""
        
        # 使用迭代代替递归来处理层级
        categories_to_process = []
        for category in self.faq_data:
            categories_to_process.append((category, 0)) # (category_data, indent_level)

        while categories_to_process:
            category, indent_level = categories_to_process.pop(0) # FIFO for breadth-first like processing, or pop() for depth-first

            # 检查是否达到最大深度限制
            if max_depth != -1 and indent_level >= max_depth:
                logger.debug(f"Skipping category due to max_depth limit: {category.get('key_path')}")
                continue

            key_path = category.get('key_path', '?') # 直接使用 key_path
            desc = category.get('category_desc', 'N/A')
            indent = "  " * indent_level  # 两个空格的缩进

            markdown_str += f"{indent}{key_path} {desc}\n"

            sub_categories = category.get("sub_category")
            if isinstance(sub_categories, list) and sub_categories:
                # 将子类别添加到待处理列表的前面，以实现深度优先遍历的顺序
                # （或者添加到末尾实现广度优先，但为了保持原递归的顺序，这里用插入到前面）
                for sub_category in reversed(sub_categories): # reversed to maintain original order after pop(0)
                     categories_to_process.insert(0, (sub_category, indent_level + 1))
        
        logger.debug("Generated category structure markdown.")
        return markdown_str
    
    @staticmethod
    def description_path_to_string(description_path: List[str]) -> str:
        return " >>> ".join(description_path)

    def get_answer_by_key_path(self, key_path: str) -> Tuple[Optional[str], Optional[str], Optional[List[str]]]:
        """根据类别键路径 (e.g., '1.1.2') 查找并返回答案和描述路径。

        Args:
            key_path: LLM 返回的类别键路径字符串。

        Returns:
            一个元组 (answer, key_path_to_answer, description_path)，其中：
            - answer: 对应的答案字符串，如果找不到则为 None。
            - key_path_to_answer: 实际答案对应的类别键路径字符串，如果找不到答案则为 None。
            - description_path: ['desc1', 'desc2'] 格式的描述路径列表，如果路径无效则为 None。
        """
        logger.debug(f"Attempting to find answer and path for key path: {key_path} using direct traversal.")

        if not key_path or not isinstance(key_path, str) or len(key_path) == 0:
            logger.warning(f"Invalid key_path received: {key_path}")
            return None, None, None

        if key_path == "0":
            logger.debug("Key path '0' received, indicating no specific category match.")
            return None, None, None

        current_nodes_list = self.faq_data
        found_path_nodes: List[Dict[str, Any]] = []  # Stack to store nodes along the traversed path
        found_path_descs: List[str] = []  # Stack to store descriptions along the traversed path
        
        key_path_segments = key_path.split('.')
        target_node_at_end_of_path: Optional[Dict[str, Any]] = None

        for i, _ in enumerate(key_path_segments):
            current_expected_full_key_path = ".".join(key_path_segments[0:i+1])
            matched_node_for_this_level: Optional[Dict[str, Any]] = None

            for node in current_nodes_list:
                if node.get('category_key_path') == current_expected_full_key_path:
                    matched_node_for_this_level = node
                    break
            
            if matched_node_for_this_level is None:
                logger.warning(f"Path segment for '{current_expected_full_key_path}' not found in data structure while traversing for '{key_path}'.")
                return None, None, None # Path segment not found

            found_path_nodes.append(matched_node_for_this_level)
            found_path_descs.append(matched_node_for_this_level.get('category_desc', 'N/A'))

            if i == len(key_path_segments) - 1:
                target_node_at_end_of_path = matched_node_for_this_level
                break # Reached the end of the key_path
            else:
                sub_categories = matched_node_for_this_level.get("sub_category")
                if isinstance(sub_categories, list) and sub_categories:
                    current_nodes_list = sub_categories
                else:
                    logger.warning(f"Path '{key_path}' expects subcategories at '{current_expected_full_key_path}', but none exist or are not a list.")
                    return None, None, None # Expected subcategories but found none
        
        if not target_node_at_end_of_path: # Should be caught by earlier returns if path is invalid
            logger.warning(f"Target node for key path '{key_path}' not identified after traversal.")
            return None, None, None

        # Search for answer by iterating backwards through the found_path_nodes (from target to root)
        for j in range(len(found_path_nodes) - 1, -1, -1):
            ancestor_node = found_path_nodes[j]
            answer = ancestor_node.get("answer")
            if answer is not None:
                # Construct description path up to this ancestor
                current_desc_path_list = found_path_descs[0:j+1]
                description_path_str = self.description_path_to_string(current_desc_path_list)
                key_path_to_answer = ancestor_node.get('category_key_path')
                logger.debug(f"Found answer for '{key_path}' at ancestor '{key_path_to_answer}'. Trail: '{description_path_str}'") # Log keeps the string format for readability
                return answer, key_path_to_answer, current_desc_path_list
        
        # If no answer found in target or any ancestor
        description_path_str = self.description_path_to_string(found_path_descs)
        logger.warning(f"No answer found for path '{key_path}' or its ancestors. Returning path: '{description_path_str}'") # Log keeps the string format for readability
        return None, None, found_path_descs

    def get_answers_by_key_path(self, key_path: str) -> List[Dict[str, Any]]:
        """根据分类键路径收集该分类及其子分类下的所有答案。

        Args:
            key_path: 分类键路径字符串，必须以'.'结尾 (e.g., '1.1.', '1.3.')。

        Returns:
            答案列表，每个答案包含：
            - answer: 答案内容
            - question_example: 问题示例
            - key_path: 答案的完整key_path
            - desc_path: 描述路径列表
        """
        logger.debug(f"Attempting to collect all answers for category key path: {key_path}")

        # 输入验证
        if not key_path or not isinstance(key_path, str) or len(key_path) == 0:
            logger.warning(f"Invalid key_path received: {key_path}")
            return []

        if not key_path.endswith('.'):
            logger.warning(f"Key path must end with '.': {key_path}")
            return []

        if key_path == "0" or key_path == "0.":
            logger.debug("Key path '0' or '0.' received, indicating no specific category match.")
            return []

        # 验证key_path格式：应该是数字.数字.数字.的格式
        key_path_without_dot = key_path.rstrip('.')
        if not key_path_without_dot:  # 只有'.'的情况
            logger.warning(f"Invalid key_path format (only dots): {key_path}")
            return []

        # 检查是否为有效的数字路径格式
        try:
            segments = key_path_without_dot.split('.')
            for segment in segments:
                if not segment.isdigit():
                    logger.warning(f"Invalid key_path format (non-numeric segment '{segment}'): {key_path}")
                    return []
        except Exception as e:
            logger.warning(f"Error validating key_path format '{key_path}': {e}")
            return []

        # 找到目标分类节点
        try:
            target_node, target_desc_path = self._find_category_node(key_path)
            if target_node is None:
                logger.warning(f"Category node not found for key path: {key_path}")
                return []
        except Exception as e:
            logger.error(f"Error finding category node for key path '{key_path}': {e}")
            return []

        # 递归收集所有答案
        try:
            all_answers = []
            self._collect_answers_recursively(target_node, target_desc_path, all_answers)

            logger.debug(f"Collected {len(all_answers)} answers for category key path: {key_path}")
            return all_answers
        except Exception as e:
            logger.error(f"Error collecting answers for key path '{key_path}': {e}")
            return []

    def _find_category_node(self, key_path: str) -> Tuple[Optional[Dict[str, Any]], Optional[List[str]]]:
        """查找指定key_path对应的分类节点。

        Args:
            key_path: 分类键路径，以'.'结尾

        Returns:
            元组 (节点, 描述路径列表)，如果找不到则返回 (None, None)
        """
        current_nodes_list = self.faq_data
        found_path_descs: List[str] = []

        # 移除末尾的'.'来分割路径
        key_path_without_dot = key_path.rstrip('.')
        if not key_path_without_dot:  # 如果只有'.'，说明是根级别
            return None, None

        key_path_segments = key_path_without_dot.split('.')

        for i, _ in enumerate(key_path_segments):
            current_expected_full_key_path = ".".join(key_path_segments[0:i+1]) + "."
            matched_node_for_this_level: Optional[Dict[str, Any]] = None

            for node in current_nodes_list:
                if node.get('key_path') == current_expected_full_key_path:
                    matched_node_for_this_level = node
                    break

            if matched_node_for_this_level is None:
                logger.warning(f"Path segment for '{current_expected_full_key_path}' not found in data structure while traversing for '{key_path}'.")
                return None, None

            found_path_descs.append(matched_node_for_this_level.get('category_desc', 'N/A'))

            if i == len(key_path_segments) - 1:
                # 到达目标节点
                return matched_node_for_this_level, found_path_descs
            else:
                # 继续到下一层级
                sub_categories = matched_node_for_this_level.get("sub_category")
                if isinstance(sub_categories, list) and sub_categories:
                    current_nodes_list = sub_categories
                else:
                    logger.warning(f"Path '{key_path}' expects subcategories at '{current_expected_full_key_path}', but none exist or are not a list.")
                    return None, None

        return None, None

    def _collect_answers_recursively(self, node: Dict[str, Any], desc_path: List[str], all_answers: List[Dict[str, Any]]):
        """使用迭代方式收集节点及其子节点的所有答案。

        Args:
            node: 当前节点
            desc_path: 当前节点的描述路径
            all_answers: 用于收集答案的列表
        """
        # 使用队列进行广度优先遍历，避免递归调用
        # 队列元素格式：(节点, 对应的描述路径)
        from collections import deque
        queue = deque([(node, desc_path)])

        while queue:
            # 从队列头部取出一个节点及其描述路径
            current_node, current_desc_path = queue.popleft()

            # 收集当前节点的candidates
            if "candidates" in current_node and isinstance(current_node["candidates"], list):
                for candidate in current_node["candidates"]:
                    answer_data = {
                        "answer": candidate.get("answer", ""),
                        "question_example": candidate.get("question_example", ""),
                        "key_path": candidate.get("key_path", ""),
                        "desc_path": current_desc_path.copy()  # 复制描述路径
                    }
                    all_answers.append(answer_data)

            # 将子分类节点加入队列，更新描述路径
            if "sub_category" in current_node and isinstance(current_node["sub_category"], list):
                for sub_node in current_node["sub_category"]:
                    # 为子节点构建新的描述路径：当前路径 + 子节点描述
                    sub_desc_path = current_desc_path + [sub_node.get("category_desc", "N/A")]
                    # 将子节点及其描述路径加入队列尾部
                    queue.append((sub_node, sub_desc_path))

    def get_key_path_from_desc_path(self, desc_path: List[str]) -> Optional[str]:
        """根据描述路径列表查找对应的类别键路径。

        这是 get_answer_by_key_path 的反向操作：
        - get_answer_by_key_path: "1.2.3" -> ['desc1', 'desc2', 'desc3']
        - get_key_path_from_desc_path: ['desc1', 'desc2', 'desc3'] -> "1.2.3"

        Args:
            desc_path: 描述路径列表，例如 ['desc1', 'desc2', 'desc3']。

        Returns:
            对应的类别键路径字符串，如果找不到则为 None。
        """
        logger.debug(f"Attempting to find key path for description path: {desc_path}")

        if not desc_path or not isinstance(desc_path, list) or len(desc_path) == 0:
            logger.warning(f"Invalid desc_path received: {desc_path}")
            return None

        current_nodes_list = self.faq_data

        for i, target_desc in enumerate(desc_path):
            matched_node_for_this_level: Optional[Dict[str, Any]] = None

            # 在当前层级中查找匹配的描述
            for node in current_nodes_list:
                if node.get('category_desc') == target_desc:
                    matched_node_for_this_level = node
                    break

            if matched_node_for_this_level is None:
                logger.warning(f"Description '{target_desc}' not found at level {i} while traversing description path {desc_path}.")
                return None

            # 如果这是最后一个描述，返回找到节点的键路径
            if i == len(desc_path) - 1:
                node_key_path = matched_node_for_this_level.get('category_key_path')
                logger.debug(f"Found complete key path '{node_key_path}' for description path {desc_path}")
                return node_key_path
            else:
                # 继续到下一层级
                sub_categories = matched_node_for_this_level.get("sub_category")
                if isinstance(sub_categories, list) and sub_categories:
                    current_nodes_list = sub_categories
                else:
                    logger.warning(f"Description path {desc_path} expects subcategories after '{target_desc}', but none exist or are not a list.")
                    return None

        # 这里不应该到达，因为循环中已经处理了所有情况
        logger.warning(f"Unexpected end of traversal for description path {desc_path}")
        return None
