import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import CONFIG from '../../config';
import SessionInfo from './SessionInfo';
import CandidateAnswers from './CandidateAnswers';
import { generateRequestIdWithPrefix } from '../../utils/requestId';

function ChatPage() {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [currentSessionId, setCurrentSessionId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [channelName, setChannelName] = useState('zulong');
  const [platformName, setPlatformName] = useState('安卓');
  const [serviceName, setServiceName] = useState('volcano');
  const messagesEndRef = useRef(null);
  // 新增状态
  const [loadingStartTime, setLoadingStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const intervalRef = useRef(null); // 用于存储 interval ID

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  // 新增 useEffect 处理计时器
  useEffect(() => {
    if (isLoading && loadingStartTime) {
      intervalRef.current = setInterval(() => {
        const seconds = (Date.now() - loadingStartTime) / 1000;
        setElapsedTime(seconds);
      }, 100); // 每 100ms 更新一次，实现 0.1 秒精度
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
    // 清理函数：组件卸载或 isLoading 变化时清除 interval
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isLoading, loadingStartTime]);

  // 获取响应头中的请求ID（统一使用小写格式）
  const getResponseRequestId = (headers) => {
    return headers['x-request-id'] || null;
  };

  // 处理切换候选答案
  const handleSelectCandidate = (msgIndex, newIndex) => {
    setMessages(prev =>
      prev.map((m, i) => {
        if (i === msgIndex && m.sender === 'ai') {
          return {
            ...m,
            selectedIndex: newIndex,
            text: m.candidateAnswers[newIndex]?.content || m.text,
          };
        }
        return m;
      })
    );
  };

  const handleSend = async () => {
    if (!input.trim() || isLoading) return;

    // --- 生成请求ID ---
    const requestId = generateRequestIdWithPrefix('chat');
    // --- 记录开始时间 (局部变量) ---
    const requestStartTime = Date.now();
    // ------------------------------

    const userMessage = {
      text: input,
      sender: 'user',
      time: new Date(requestStartTime).toLocaleTimeString(),
      requestId: requestId // 添加请求ID到用户消息
    };
    const currentInput = input;
    setMessages(prev => [...prev, userMessage]);
    setInput('');

    setIsLoading(true);
    // --- 更新状态用于驱动实时计时器 ---
    setLoadingStartTime(requestStartTime); // 使用同一个时间戳更新状态
    setElapsedTime(0);
    // --------------------------------

    try {
      // 准备符合 API 格式的消息历史
      const historyMessages = messages
        .filter(msg => msg.sender !== 'error') // 过滤掉错误消息
        .map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.text
        }));

      // 添加当前用户输入的消息
      historyMessages.push({ role: 'user', content: currentInput });

      // 发送到后端代理
      const url = `${CONFIG.API_BASE_URL}/chat/faq_filter`;
      const requestData = {
        conversation: historyMessages,
        session_id: currentSessionId,
        service: serviceName,
        context_params: {
          channel_name: channelName,
          platform_name: platformName,
        }
      };

      // 添加请求ID到HTTP头（统一使用小写格式）
      const requestConfig = {
        headers: {
          'x-request-id': requestId
        }
      };

      console.log('Sending request to:', url);
      console.log('Request ID (sent):', requestId);
      console.log('Request data:', requestData);
      const response = await axios.post(url, requestData, requestConfig);
      console.log('Received response:', response.data);

      const requestEndTime = Date.now();
      const responseRequestId = getResponseRequestId(response.headers);
      console.log('Response Request ID (received):', responseRequestId);

      // --- 处理 AI 回复 (统一按 JSON 候选答案格式处理) --- 
      let aiResponseText = '抱歉，未能获取到回复。';
      let candidates = [];
      let selectedIndex = 0;
      // --- 使用局部变量计算精确耗时 --- 
      const durationSeconds = requestStartTime ? (requestEndTime - requestStartTime) / 1000 : 0;
      // --------------------------------

      if (response.data.response_code === 200 && Array.isArray(response.data.response_body) && response.data.response_body.length > 0) {
        // 始终尝试将 response_text 解析为 JSON 数组 (候选答案列表)
        candidates = response.data.response_body;
        // 如果解析成功且是包含内容的数组，取第一个作为默认回复
        aiResponseText = candidates[0].content;
        selectedIndex = 0;

      } else {
        // 如果 JSON 解析失败，说明返回的不是预期的 JSON 格式，直接将原始文本作为回复
        throw new Error("后台报错:" + response.data.response_text);
      }

      const aiMessage = {
        text: aiResponseText,
        candidateAnswers: candidates,
        selectedIndex: selectedIndex,
        sender: 'ai',
        time: new Date(requestEndTime).toLocaleTimeString(),
        sessionId: response.data.session_id,
        usages: response.data.usages,
        classifyThinking: response.data.classify_thinking,
        rewrittenQuery: response.data.rewritten_query,
        loadingDuration: durationSeconds.toFixed(1), // 使用精确计算的耗时
        requestId: requestId, // 前端发送的请求ID
        responseRequestId: responseRequestId // 后端返回的请求ID（严格显示后端实际返回内容）
      };
      // --- 处理结束 ---

      console.log('AI message:', aiMessage);

      setMessages(prev => [...prev, aiMessage]);
      setCurrentSessionId(response.data.session_id);

    } catch (error) {
      console.error('Error sending message:', error);
      let errorText = '未知错误';
      if (error.response) {
        // HTTP 错误，优先打印 detail 字段
        errorText = error.response.data?.detail || error.response.data?.error || 'HTTP请求失败';
      } else if (error.message) {
        errorText = error.message;
      }
      const errorMessage = { 
          text: errorText, 
          sender: 'error', 
          time: new Date().toLocaleTimeString() 
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setLoadingStartTime(null); // 清空状态，停止实时计时器
    }
  };

  // 新增：清空聊天记录和会话状态的函数
  const handleClear = () => {
    setMessages([]);
    setCurrentSessionId(null);
  };

  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map((msg, index) => (
          <div key={index} className={`message-row ${msg.sender}`}>
            {msg.sender === 'ai' && (
              <>
                <div className="avatar ai-avatar">🤖</div>
                <div className="message-content-wrapper">
                  {msg.responseRequestId && (
                    <div className="request-id-display ai-request-id">
                      响应ID: {msg.responseRequestId}
                    </div>
                  )}
                  {!msg.responseRequestId && (
                    <div className="request-id-display ai-request-id missing">
                      响应ID: 未返回
                    </div>
                  )}
                  <div className={`message ${msg.sender}`}>{msg.text}</div>
                  {msg.rewrittenQuery && msg.rewrittenQuery.length > 0 && (
                    <div className="rewritten-query-process">
                      <div className="rewritten-query-title">玩家问题重写：</div>
                      <pre className="rewritten-query-content">{msg.rewrittenQuery}</pre>
                    </div>
                  )}
                  <CandidateAnswers
                    answers={msg.candidateAnswers}
                    selectedIndex={msg.selectedIndex}
                    onSelect={newIdx => handleSelectCandidate(index, newIdx)}
                    groupName={`candidate-${index}`}
                  />
                  {msg.classifyThinking && msg.classifyThinking.length > 0 && (
                    <div className="thinking-process">
                      <div className="thinking-title">思考过程：</div>
                      <pre className="thinking-content">{msg.classifyThinking}</pre>
                    </div>
                  )}
                  <div className="message-footer">
                    <SessionInfo sessionId={msg.sessionId} usages={msg.usages} />
                    <div className="timestamp">{msg.time}</div>
                    {msg.loadingDuration && (
                      <span className="loading-duration-display">
                        (耗时: {msg.loadingDuration}s)
                      </span>
                    )}
                  </div>
                </div>
              </>
            )}
            {msg.sender === 'error' && (
              <>
                <div className="avatar error-avatar">⚠️</div>
                <div className="message-content-wrapper">
                  <div className={`message ${msg.sender}`}>{msg.text}</div>
                </div>
              </>
            )}
            {msg.sender === 'user' && (
              <>
                <div className="avatar user-avatar">🧑</div>
                <div className="message-content-wrapper user">
                  {msg.requestId && (
                    <div className="request-id-display user-request-id">
                      请求ID: {msg.requestId}
                    </div>
                  )}
                  <div
                    className={`message ${msg.sender}`}
                    onClick={() => {
                      if (!isLoading) {
                        setInput(msg.text);
                      }
                    }}
                    style={{ cursor: !isLoading ? 'pointer' : 'default' }}
                  >
                    {msg.text}
                  </div>
                  <div className="message-footer user">
                      <div className="timestamp">{msg.time}</div>
                  </div>
                </div>
              </>
            )}
          </div>
        ))}
        {isLoading && (
          <div className="message-row ai">
            <div className="avatar ai-avatar">⏳</div>
            <div className="message-content-wrapper">
              <div className="message ai loading-indicator">
                <span className="loading-dots">
                  <span>.</span><span>.</span><span>.</span>
                </span>
                {loadingStartTime && (
                  <span className="loading-timer">
                     ({elapsedTime.toFixed(1)}s)
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <div className="biz-params-selectors">
        <label>
          渠道:
          <select value={channelName} onChange={(e) => setChannelName(e.target.value)} disabled={isLoading}>
            <option value="zulong">zulong</option>
            <option value="xiaomi">xiaomi</option>
            <option value="huawei">huawei</option>
            <option value="苹果">苹果</option>
          </select>
        </label>
        <label>
          平台:
          <select value={platformName} onChange={(e) => setPlatformName(e.target.value)} disabled={isLoading}>
            <option value="安卓">安卓</option>
            <option value="鸿蒙">鸿蒙</option>
            <option value="iOS">iOS</option>
            <option value="iOS越狱">iOS越狱</option>
            <option value="PC">PC</option>
          </select>
        </label>
      </div>
      <div className="input-area">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSend()}
          placeholder="输入消息..."
          disabled={isLoading}
        />
        <div className="send-service-group">
          <button className="send-btn" onClick={handleSend} aria-label="发送" disabled={isLoading}>
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/></svg>
          </button>
          <select className="service-select" value={serviceName} onChange={(e) => setServiceName(e.target.value)} disabled={isLoading}>
            <option value="volcano">Agent(火山方舟)</option>
            <option value="bailian">Agent(阿里百炼)</option>
          </select>
        </div>
        <button className="clear-btn" onClick={handleClear} aria-label="清空" disabled={isLoading}>
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" clipRule="evenodd" d="M6 5C5.44772 5 5 5.44772 5 6V7H4C3.44772 7 3 7.44772 3 8C3 8.55228 3.44772 9 4 9H5V18C5 19.6569 6.34315 21 8 21H16C17.6569 21 19 19.6569 19 18V9H20C20.5523 9 21 8.55228 21 8C21 7.44772 20.5523 7 20 7H19V6C19 5.44772 18.5523 5 18 5C17.4477 5 17 5.44772 17 6V7H7V6C7 5.44772 6.55228 5 6 5ZM8 9H16V18C16 18.5523 15.5523 19 15 19H9C8.44772 19 8 18.5523 8 18V9ZM11 11C10.4477 11 10 11.4477 10 12V16C10 16.5523 10.4477 17 11 17C11.5523 17 12 16.5523 12 16V12C12 11.4477 11.5523 11 11 11ZM14 11C13.4477 11 13 11.4477 13 12V16C13 16.5523 13.4477 17 14 17C14.5523 17 15 16.5523 15 16V12C15 11.4477 14.5523 11 14 11Z" fill="currentColor"/></svg>
        </button>
      </div>
    </div>
  );
}

export default ChatPage; 