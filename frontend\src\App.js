import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Navigation from './components/common/Navigation';
import ChatPage from './components/chat/ChatPage';
import BatchTestPage from './components/batch/BatchTestPage';
import './styles/App.css';
import './styles/chat.css';
import './styles/batch.css';

function App() {
  return (
    <Router>
      <div className="app">
        <Navigation />
        <div className="app-content">
          <Routes>
            <Route path="/chat" element={<ChatPage />} />
            <Route path="/batch-test" element={<BatchTestPage />} />
            <Route path="/" element={<Navigate to="/chat" replace />} />
          </Routes>
        </div>
      </div>
    </Router>
  );
}

export default App;
