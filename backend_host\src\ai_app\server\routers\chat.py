from fastapi import APIRouter, HTTPException

# 导入前端请求/响应模型和特定服务的调用函数
from ai_app.config import config
from ai_app.models.chat import ChatRequest, ChatResponse
from ai_app.services.bailian import call_bailian_api
from ai_app.services.coze import call_coze_api
from ai_app.utils.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

router = APIRouter()

@router.post("/chat", response_model=ChatResponse)
async def chat_proxy(chat_request: ChatRequest):
    """处理聊天请求，将请求路由到相应的后端服务。"""
    logger.info("=========== /chat endpoint received request ==========")
    try:
        # --- 从 chat_request 直接获取要调用的服务，默认为 bailian --- # 注意：此处修改了默认服务为 Bailian
        service_to_call = "bailian" # 默认值修改为 bailian
        if chat_request.service:
            service_to_call = chat_request.service.lower()
            logger.info(f"Service specified in request body: '{service_to_call}'")
        else:
            # logger.info("No service specified in request body, defaulting to 'agent:volcano'") # 旧日志
            logger.info("No service specified in request body, defaulting to 'bailian'") # 新日志

        # --- 根据服务名称调用相应的 API --- (移除原有的 context_params 检查)
        if service_to_call == "bailian":
            logger.info("Routing request to Bailian service.")
            config.check_bailian_vars()
            return await call_bailian_api(chat_request)

        elif service_to_call == "coze":
            logger.info("Routing request to Coze service.")
            config.check_coze_vars()
            return await call_coze_api(chat_request) # 调用 Coze 服务

        else:
             # 如果服务名称无效
             logger.error(f"Unknown service requested: {service_to_call}")
             raise HTTPException(status_code=400, detail=f"Invalid service requested: {service_to_call}")

    except HTTPException as e:
        # 直接重新抛出由服务层或本层抛出的 HTTPException
        # 服务层会处理它们自己的配置、连接、解析等错误
        raise e
    except Exception as e:
        # 捕获未预料的错误
        logging.exception("An unexpected error occurred in /chat endpoint")
        raise HTTPException(status_code=500, detail="Internal server error.") 