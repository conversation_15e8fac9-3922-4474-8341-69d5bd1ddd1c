import json
import os # Added os import
from typing import Dict, Any

# Import the necessary classes from other modules
from ai_app.agents.faq_filter_agent.data_parser import FAQDataParser
from ai_app.agents.faq_filter_agent.llm_clients import QueryRewriteClient, FAQClassifierClient, FAQRerankClient, FAQRetrieveClient
from ai_app.models.chat import ChatRequest, ChatModelUsages # Adjusted import path
from ai_app.models.chat_to_faq_filter import ChatToFaqFilterCandidate, ChatToFaqFilterResponse
from ai_app.agents.faq_filter_agent.exceptions import ConfigurationError, PromptLoadError, EmbeddingCollectionNotFoundError # Import custom exceptions
# Import the specific LLM implementation
from ai_app.agents.faq_filter_agent.llm_impl.volcano_impl import VolcanoLLMImpl, VolcanoEmbeddingImpl
from ai_app.agents.faq_filter_agent.llm_impl.bailian_impl import BailianLLMImpl
from ai_app.agents.faq_filter_agent.llm_impl.rerank_impl import RerankImpl
from ai_app.config import config
from ai_app.utils.request_tracing import get_traced_logger
# TODO: Consider adding custom exceptions from exceptions.py

# Setup logger for this module
logger = get_traced_logger(__name__)

class FAQFilterAgent:
    """AI Agent 的主入口和协调器。"""

    def __init__(self, context_params: Dict[str, Any] = None, model_platform: str = None):
        """初始化 Agent，从 config.py 加载配置并创建依赖项。"""

        try:
            self.channel_name = None
            if context_params is not None and context_params.get('channel_name') is not None:
                self.channel_name = context_params['channel_name']

            # Resolve absolute paths for files based on AGENT_DIR
            faq_file_path = config.faq_file_path
            if self.channel_name:
                # 同目录下查看是否存在指定channel的faq文件
                channel_specific_faq_file_path = faq_file_path.replace('.json', f'-{self.channel_name}.json')
                if os.path.exists(channel_specific_faq_file_path):
                    faq_file_path = channel_specific_faq_file_path
                else:
                    logger.debug(f"Channel-specific FAQ file not found: {channel_specific_faq_file_path}")
                    logger.debug(f"Using default FAQ file: {faq_file_path}")

            # Load prompts
            try:
                with open(config.rewrite_prompt_path, 'r', encoding='utf-8') as f:
                    rewrite_prompt = f.read()
                with open(config.classify_prompt_path, 'r', encoding='utf-8') as f:
                    classify_prompt = f.read()
                logger.debug("Successfully loaded prompt files.")
            except FileNotFoundError as e:
                logger.error(f"Prompt file not found: {e}")
                raise PromptLoadError(f"Required prompt file not found: {e}") from e
            except IOError as e:
                 logger.error(f"Error reading prompt file: {e}")
                 raise PromptLoadError(f"Could not read prompt file: {e}") from e

            # Initialize LLM Implementation for rewrite/classify based on config
            api_key, api_base, model_id = config.get_model_config(model_platform)
            if model_platform == "volcano":
                llm_impl = VolcanoLLMImpl(api_key, api_base, model_id)
            elif model_platform == "bailian":
                llm_impl = BailianLLMImpl(api_key, api_base, model_id)
            else:
                raise ValueError(f"Invalid model platform for rewrite/classify: {model_platform}")
            
            rerank_api_key, rerank_api_base, rerank_model_id = config.get_rerank_config()
                
            # Initialize components
            self.faq_parser = FAQDataParser(faq_file_path=faq_file_path)
            self.rewrite_client = QueryRewriteClient(
                llm_client=llm_impl, 
                prompt_template=rewrite_prompt
            )
            self.classifier_client = FAQClassifierClient(
                llm_client=llm_impl, 
                prompt_template=classify_prompt
            )

            if rerank_api_key and rerank_api_base and rerank_model_id:
                self.rerank_client = FAQRerankClient(llm_client=RerankImpl(
                    api_key=rerank_api_key,
                    api_base=rerank_api_base,
                    model_id=rerank_model_id
                ))
            else:
                self.rerank_client = None

            # Initialize retrieve client for fallback retrieval
            volcano_knowledge_api_base, volcano_knowledge_api_ak, volcano_knowledge_api_sk, volcano_knowledge_collection_name, volcano_knowledge_project = config.get_volcano_knowledge_config()
            if volcano_knowledge_api_base and volcano_knowledge_api_ak and volcano_knowledge_api_sk:
                # 保存基础配置信息，collection_name将在调用时动态确定
                self.knowledge_collection_name = volcano_knowledge_collection_name
                embedding_impl = VolcanoEmbeddingImpl(
                    api_base=volcano_knowledge_api_base,
                    api_ak=volcano_knowledge_api_ak,
                    api_sk=volcano_knowledge_api_sk,
                    project=volcano_knowledge_project
                )
                self.retrieve_client = FAQRetrieveClient(embedding_impl)
            else:
                self.retrieve_client = None
                self.knowledge_collection_name = None
                self.channel_name = None
                
        except KeyError as e:
            logger.error(f"Missing configuration key: {e}")
            raise ConfigurationError(f"Missing required configuration key: {e}") from e
        except (ConfigurationError, PromptLoadError) as e:
            # Re-raise exceptions related to config/prompt loading
            logger.error(f"Failed to initialize FAQFilterAgent due to error: {e}")
            raise
        except Exception as e:
            # Catch any other unexpected errors during initialization
            logger.exception(f"Unexpected error during FAQFilterAgent initialization: {e}")
            raise ConfigurationError(f"An unexpected error occurred during agent initialization: {e}") from e

    async def process_user_request(self, chat_request: ChatRequest) -> ChatToFaqFilterResponse:
        """处理用户请求的完整流程。

        Args:
            chat_request: 包含对话历史和上下文信息的请求对象。

        Returns:
            一个包含响应文本和会话 ID 的响应对象。
        """
        logger.info(f"--- FAQFilterAgent: process_user_request called (Session ID: {chat_request.session_id}) ---")

        # Initialize usages list
        model_usages_list = [] # MODIFIED: To collect all usages

        # Extract conversation history and context from ChatRequest
        # Convert ChatInputMessage objects to the dict format expected by rewrite_client
        conversation_dicts = [{"role": msg.role, "content": msg.content} for msg in chat_request.conversation]
        context = chat_request.context_params or {} # Use context_params if available

        # Prepare input data for the rewrite client
        rewrite_input_data = {
            "conversation": conversation_dicts,
            "context": context
        }

        # 1. 查询重写 (Query Rewrite)
        try:
            rewritten_query, rewritten_usage = await self.rewrite_client.rewrite_query(input_data=rewrite_input_data)
            if rewritten_usage: # MODIFIED: Add usage if available
                model_usages_list.append(rewritten_usage)

            if not rewritten_query:
                logger.error("Failed to rewrite query: LLM did not return expected 'query_rewrite' field.")
                return ChatToFaqFilterResponse( # Typo in original: ChatToFqFilterResponse -> ChatToFaqFilterResponse
                    response_code=500,
                    response_text="Failed to understand the query context.",
                    session_id=chat_request.session_id
                )
            logger.info(f"Rewritten Query: {rewritten_query}")
        except Exception as e: 
             logger.exception(f"Error during query rewrite: {e}")
             return ChatToFaqFilterResponse(
                 response_code=500,
                 response_text="An error occurred while processing your query context.",
                 session_id=chat_request.session_id
             )

        # 2. 获取 FAQ 目录结构
        try:
            faq_structure_md = self.faq_parser.get_category_structure_markdown()
            if not faq_structure_md:
                 logger.error("Failed to get FAQ structure: Parser returned empty structure.")
                 return ChatToFaqFilterResponse(
                     response_code=500,
                     response_text="Failed to load internal knowledge base.",
                     session_id=chat_request.session_id
                 )
        except Exception as e:
            logger.exception(f"Error getting FAQ structure: {e}")
            return ChatToFaqFilterResponse(
                 response_code=500,
                 response_text="An error occurred accessing internal knowledge base.",
                 session_id=chat_request.session_id
            )

        # 3. 问题分类 (Classification)
        try:
            classification_data, classification_usage, classification_thinking = await self.classifier_client.classify_query(rewritten_query, faq_structure_md)
            if classification_usage: # MODIFIED: Add usage if available
                model_usages_list.append(classification_usage)

            if not classification_data or not all('category_key_path' in item for item in classification_data):
                logger.error("Failed to classify query: LLM did not return expected 'category_key_path' field.")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="Failed to classify the query. Wrong-format response from LLM.",
                    session_id=chat_request.session_id
                )

            result_list = []
            for index, item in enumerate(classification_data):
                result = {
                    'category_key_path': item['category_key_path'],
                    'reason': item.get('reason', '') # Ensure reason exists
                }
                logger.info(f"Classification({index}) Path: {result['category_key_path']}")
                result_list.append(result)
        except Exception as e:
            logger.exception(f"Error during query classification: {e}")
            return ChatToFaqFilterResponse(
                 response_code=500,
                 response_text="An error occurred while classifying your query.",
                 session_id=chat_request.session_id
             )
        
        # 3.1 问题召回（作为分类失败时的补充）
        # 当result_list为空，或者其中仅包含category_key_path为"0"的项目，则执行召回尝试
        should_retrieve = False
        if not result_list:
            should_retrieve = True
            logger.info("Classification result is empty, attempting retrieval fallback.")
        elif all(item.get('category_key_path') == '0' for item in result_list):
            should_retrieve = True
            result_list = []
            logger.info("All classification results have category_key_path='0', attempting retrieval fallback.")

        if should_retrieve and self.retrieve_client:
            try:
                logger.info("Starting retrieval fallback...")

                # 构建collection_name
                collection_name = self.knowledge_collection_name
                if self.channel_name:
                    # 不同渠道的知识库通过后缀区分，这是在火山平台上约定好的
                    collection_name += f"_{self.channel_name}"

                retrieve_results = None
                retrieve_usage = None

                try:
                    # 首先尝试带channel后缀的collection
                    retrieve_results, retrieve_usage = await self.retrieve_client.retrieve_from_query(
                        query=rewritten_query,
                        collection_name=collection_name,
                        top_n=3  # 最多召回3条
                    )
                except EmbeddingCollectionNotFoundError as e:
                    logger.info(f"Collection with channel suffix not found: {collection_name}, trying base collection.")
                    # 如果带channel后缀的collection不存在，尝试不带后缀的基础collection
                    base_collection_name = self.knowledge_collection_name
                    try:
                        retrieve_results, retrieve_usage = await self.retrieve_client.retrieve_from_query(
                            query=rewritten_query,
                            collection_name=base_collection_name,
                            top_n=3  # 最多召回3条
                        )
                        logger.info(f"Successfully retrieved from base collection: {base_collection_name}")
                    except EmbeddingCollectionNotFoundError as e2:
                        logger.error(f"Base collection also not found: {base_collection_name}: {e2}")
                        raise e2  # 重新抛出异常，让外层处理

                if retrieve_results is not None and retrieve_usage:
                    model_usages_list.append(retrieve_usage)

                    # 将召回结果添加到result_list中，仿照步骤3的逻辑
                    for index, retrieve_item in enumerate(retrieve_results):
                        result = {
                            'category_key_path': retrieve_item['category_key_path'],
                            'reason': f"向量召回：{retrieve_item['reason']}"
                        }
                        logger.info(f"Retrieval knowledge({index}). Path: {retrieve_item['category_key_path']}, score: {retrieve_item['reason']}")
                        result_list.append(result)

            except Exception as e:
                logger.exception(f"Error during retrieval fallback: {e}")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="An error occurred while retrieving supplementary information.",
                    session_id=chat_request.session_id
                )
        elif should_retrieve and not self.retrieve_client:
            logger.warning("Retrieval fallback needed but retrieve_client not available.")
        else:
            logger.info("Classification results are sufficient, skipping retrieval fallback.")

        # 4. 答案检索
        for result in result_list: # result_list now contains dicts like {'category_key_path': ..., 'reason': ...}
            try:
                final_answer, key_path_to_answer, desc_path = self.faq_parser.get_answer_by_key_path(result['category_key_path'])
                result['final_answer'] = final_answer
                result['key_path_to_answer'] = key_path_to_answer
                result['desc_path'] = desc_path
                result['score'] = 0.0 if final_answer else -1.0 # 0.0 for retrieved answers, -1.0 for no answer
            except Exception as e:
                logger.exception(f"Error during answer retrieval for path '{result['category_key_path']}': {e}")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="An error occurred while retrieving the answer.",
                    session_id=chat_request.session_id
                )

        # 5. 答案重排 (Reranking) - MODIFIED SECTION
        if self.rerank_client: # Check if rerank_client was initialized successfully
            try:
                # rerank_retrieve_results expects a list of dicts, and will add/update 'score' in them
                # It returns the sorted list and usage.
                result_list, rerank_usage = await self.rerank_client.rerank_retrieve_results(rewritten_query, result_list)
                
                if rerank_usage:
                    model_usages_list.append(rerank_usage)
                logger.info(f"Reranking completed. Result list size: {len(result_list)}. First item score (if any): {result_list[0].get('score') if result_list else 'N/A'}")

            except Exception as e:
                logger.exception(f"Error during answer reranking: {e}")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="An error occurred while reranking the answer.",
                    session_id=chat_request.session_id
                )
        else:
            logger.info("Rerank client not available or not initialized. Skipping reranking step.")

        # 6. 返回结果
        candidates = []
        # 定义保底答案
        fallback_answer = "<保底话术>未找到具体答案。"
        # 将所有使用量合并到usages中
        usages = ChatModelUsages(models=[usage for usage in model_usages_list if usage])

        for result in result_list:
            score = result['score']
            if result['desc_path'] is not None:
                # desc_path不为空表明类别键路径是有效的
                desc_path_str = self.faq_parser.description_path_to_string(result['desc_path'])
                answer_size = 'N/A' if result['final_answer'] is None else len(result['final_answer'])
                final_answer = result['final_answer'] or fallback_answer
                key_path_to_answer = result['key_path_to_answer'] or "0"
                final_reason = f"分类路径：{result['category_key_path']}；采纳路径：{key_path_to_answer}\n路径描述：{desc_path_str}"
                if result['reason']:
                    final_reason += f"\n{result['reason']}"
                logger.info(f"Retrieved Answer by path({key_path_to_answer}): {desc_path_str}, answer_size: {answer_size}, score: {score}")
                candidates.append(ChatToFaqFilterCandidate(
                    content=final_answer,
                    category_chain=desc_path_str,
                    score=score,
                    reason=final_reason
                ))
            else:
                # 未找到具体答案
                logger.info("No specific answer found for the query.")
                candidates.append(ChatToFaqFilterCandidate(
                    content=fallback_answer,
                    category_chain="",
                    score=score, 
                    reason=f"分类路径：{result['category_key_path']}\n无可采纳答案"
                ))

        return ChatToFaqFilterResponse(
            response_code=200,
            response_body=candidates,
            session_id=chat_request.session_id,
            usages=usages,
            rewritten_query=rewritten_query,
            classify_thinking=classification_thinking,
        )
