# 前端应用

这是一个基于 React 的智能客服FAQ筛选应用前端。

## 项目特性

- **智能问答**: 支持多轮对话的AI智能客服系统
- **候选答案**: 显示多个候选答案供用户选择，包含分数和原因
- **会话管理**: 支持会话ID追踪和使用统计信息
- **多平台支持**: 支持不同渠道和平台的配置
- **实时反馈**: 显示请求耗时和加载状态

## 技术栈

- **React 19.1.0**: 前端框架
- **Axios**: HTTP 客户端
- **Create React App**: 项目脚手架

## 开发环境

### 前置要求

- Node.js (推荐 16+ 版本)
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 环境配置

**开发环境配置**：
复制 `.env.example` 文件为 `.env` 并根据需要修改：

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
# .env
REACT_APP_API_BASE_URL=http://localhost:8000/api/v1
```

**Docker环境配置**：
Docker部署时会自动从环境变量生成配置，无需手动修改。

### 启动开发服务器

```bash
npm start
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 构建部署

### 生产构建

```bash
npm run build
```

构建文件将输出到 `build` 目录。

### 测试

```bash
npm test
```

## 项目结构

```
src/
├── App.js              # 主应用组件
├── App.css             # 主样式文件
├── config.js           # 配置管理（支持运行时和构建时配置）
├── index.js            # 应用入口
├── components/         # React组件
├── styles/             # 样式文件
└── utils/              # 工具函数

public/
├── config.js           # 开发环境配置文件
├── index.html          # HTML模板
└── ...
```

## 主要功能

### 智能对话
- 支持多轮对话历史记录
- 实时显示回复时间
- 错误处理和重试机制

### 候选答案系统
- 显示AI生成的多个候选答案
- 每个答案包含置信度分数
- 支持用户选择最佳答案

### 配置管理
- 渠道名称设置
- 平台类型选择
- 服务类型配置

## API集成

应用通过以下端点与后端通信：

- `POST /chat/faq_filter` - 发送对话请求

## 配置管理说明

### 开发环境
**方式1（推荐）**: 使用 `.env` 文件
- 复制 `.env.example` 为 `.env` 并修改配置
- 支持标准的 `REACT_APP_*` 环境变量
- 修改后重启开发服务器生效
- 配置文件不会被提交到版本控制

**方式2（备选）**: 使用 `public/config.js` 文件
- 直接编辑 `public/config.js` 文件
- 修改后刷新页面即可生效
- 适合临时调试

### Docker环境
- 使用 `docker/frontend/.env` 文件配置
- 容器启动时动态生成 `config.js`
- 修改配置后重启容器即可生效，无需重新构建镜像

### 配置优先级
1. **运行时配置** (Docker环境): `window.APP_CONFIG`
2. **构建时配置** (开发环境): `process.env.REACT_APP_*` (来自.env文件)
3. **默认配置**: 硬编码的默认值

## 开发说明

- 组件采用函数式组件和React Hooks
- 状态管理使用React内置的useState和useEffect
- 样式使用CSS文件，支持响应式设计
- 支持开发环境和Docker环境的灵活配置切换
